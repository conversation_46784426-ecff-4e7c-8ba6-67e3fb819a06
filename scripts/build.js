#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get build target from command line args
const target = process.argv[2] || 'web';

console.log(`🚀 Building for target: ${target}`);

// Set environment variables based on target
const env = { ...process.env };

switch (target) {
  case 'static':
    env.BUILD_TARGET = 'static';
    env.NODE_ENV = 'production';
    console.log('📦 Static build mode - Generating full static export for offline-first operation');
    break;
  case 'electron':
    env.BUILD_TARGET = 'electron';
    env.NODE_ENV = 'production';
    console.log('🖥️  Electron build mode - Static export enabled with electron-optimized settings');
    break;
  case 'mobile':
    env.BUILD_TARGET = 'mobile';
    env.NODE_ENV = 'production';
    console.log('📱 Mobile build mode - Capacitor-optimized build');
    break;
  case 'web':
    env.BUILD_TARGET = 'web';
    env.NODE_ENV = 'production';
    console.log('🌐 Web build mode - Landing page only, restaurant functionality excluded');
    break;
  case 'server':
  default:
    env.BUILD_TARGET = 'server';
    env.NODE_ENV = 'production';
    console.log('🌐 Server build mode - Full server-side rendering with API routes');
    break;
}

// 🔧 Enhanced build validation and pre-build checks
function validateBuildEnvironment() {
  console.log('🔍 Validating build environment...');
  
  // Check if we have the required config files
  const requiredFiles = ['next.config.ts', 'package.json'];
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, '..', file))) {
      throw new Error(`❌ Required file missing: ${file}`);
    }
  }
  
  // For electron builds, check if electron directory exists
  if (target === 'electron') {
    const electronDir = path.join(__dirname, '..', 'electron');
    if (!fs.existsSync(electronDir)) {
      throw new Error('❌ Electron directory not found. Run electron setup first.');
    }
  }
  
  console.log('✅ Build environment validation passed');
}

// 🧹 Clean previous builds
function cleanPreviousBuilds() {
  console.log('🧹 Cleaning previous builds...');
  
  const dirsToClean = ['.next', 'out'];
  if (target === 'electron') {
    dirsToClean.push('electron/app', 'electron/dist');
  }
  
  for (const dir of dirsToClean) {
    const dirPath = path.join(__dirname, '..', dir);
    if (fs.existsSync(dirPath)) {
      console.log(`  Removing ${dir}...`);
      fs.rmSync(dirPath, { recursive: true, force: true });
    }
  }
  
  console.log('✅ Previous builds cleaned');
}

// 🚫 Temporarily hide API directory for static builds
function hideApiDirectory() {
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const apiDir = path.join(__dirname, '..', 'app', 'api');
    const hiddenApiDir = path.join(__dirname, '..', 'app', '_api_hidden');

    if (fs.existsSync(apiDir)) {
      console.log('🚫 Temporarily hiding API directory for static build...');
      fs.renameSync(apiDir, hiddenApiDir);
    }
  }
}

// 🔄 Restore API directory after build
function restoreApiDirectory() {
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const apiDir = path.join(__dirname, '..', 'app', 'api');
    const hiddenApiDir = path.join(__dirname, '..', 'app', '_api_hidden');

    if (fs.existsSync(hiddenApiDir)) {
      console.log('🔄 Restoring API directory...');
      fs.renameSync(hiddenApiDir, apiDir);
    }
  }
}

// 📱 Hide mobile-incompatible pages for mobile builds
function hideMobileIncompatiblePages() {
  if (target === 'mobile') {
    console.log('📱 Hiding mobile-incompatible pages...');
    execSync('node scripts/hide-mobile-incompatible-pages.js hide', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
  }
}

// 🔄 Restore mobile-incompatible pages after build
function restoreMobileIncompatiblePages() {
  if (target === 'mobile') {
    console.log('🔄 Restoring mobile-incompatible pages...');
    execSync('node scripts/hide-mobile-incompatible-pages.js restore', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
  }
}

// 📊 Validate static export completeness
function validateStaticExport() {
  console.log('🔍 Validating static export completeness...');
  
  const outDir = path.join(__dirname, '..', 'out');
  if (!fs.existsSync(outDir)) {
    throw new Error('❌ Static export failed: out/ directory not found');
  }
  
  // Check for essential files
  const essentialFiles = ['index.html', '_next'];
  for (const file of essentialFiles) {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  Missing essential file/directory: ${file}`);
    }
  }
  
  // Count exported pages
  const htmlFiles = [];
  function findHtmlFiles(dir) {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const itemPath = path.join(dir, item);
      if (fs.statSync(itemPath).isDirectory()) {
        findHtmlFiles(itemPath);
      } else if (item.endsWith('.html')) {
        htmlFiles.push(path.relative(outDir, itemPath));
      }
    }
  }
  
  findHtmlFiles(outDir);
  console.log(`📄 Found ${htmlFiles.length} HTML pages in export:`);
  htmlFiles.forEach(file => console.log(`  - ${file}`));
  
  if (htmlFiles.length < 3) {
    console.warn('⚠️  Suspiciously few HTML pages exported. Check for export issues.');
  }
  
  console.log('✅ Static export validation completed');
}

try {
  // Pre-build validation
  validateBuildEnvironment();
  cleanPreviousBuilds();
  hideApiDirectory(); // Hide API directory for static builds
  hideMobileIncompatiblePages(); // Hide mobile-incompatible pages for mobile builds
  
  // Run the build with enhanced logging
  console.log('⚡ Running Next.js build with static export...');
  console.log(`📊 Build target: ${target}`);
  console.log(`🔧 NODE_ENV: ${env.NODE_ENV}`);
  console.log(`🎯 BUILD_TARGET: ${env.BUILD_TARGET}`);
  
  execSync('npx next build', { 
    stdio: 'inherit', 
    env,
    cwd: path.join(__dirname, '..') 
  });
  
  restoreApiDirectory(); // Restore API directory after build
  restoreMobileIncompatiblePages(); // Restore mobile-incompatible pages after build
  
  // 🔍 For static/electron/mobile builds, validate the export
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    validateStaticExport();
  }
  
  // Post-build steps based on target
  switch (target) {
    case 'electron':
      console.log('🖥️  Copying static export to Electron app directory...');
      execSync('node scripts/copy-out-to-electron.js', { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      
      // Validate Electron app directory
      const electronAppDir = path.join(__dirname, '..', 'electron', 'app');
      if (fs.existsSync(electronAppDir)) {
        const appFiles = fs.readdirSync(electronAppDir);
        console.log(`📁 Electron app directory contains ${appFiles.length} items`);
        
        // Check for critical files
        if (!appFiles.includes('index.html')) {
          console.warn('⚠️  index.html not found in electron/app - app may not start properly');
        }
        if (!appFiles.some(f => f.startsWith('_next'))) {
          console.warn('⚠️  _next assets not found in electron/app - app may not function properly');
        }
      }
      break;
      
    case 'mobile':
      console.log('📱 Static export ready for Capacitor deployment...');
      console.log('💡 Next steps:');
      console.log('  1. Run: npx cap sync');
      console.log('  2. Run: npx cap build');
      break;
      
    case 'static':
      console.log('📦 Static export completed successfully!');
      console.log('💡 Files are ready in the out/ directory');
      break;
  }
  
  // 📊 Build summary
  console.log('\n🎉 Build Summary:');
  console.log(`✅ Target: ${target}`);
  console.log(`✅ Environment: ${env.NODE_ENV}`);
  
  if (target === 'static' || target === 'electron' || target === 'mobile') {
    const outDir = path.join(__dirname, '..', 'out');
    if (fs.existsSync(outDir)) {
      const stats = fs.statSync(outDir);
      console.log(`📁 Export directory: out/ (last modified: ${stats.mtime.toISOString()})`);
    }
  }
  
  console.log(`🚀 Build completed successfully for ${target}!`);
  
} catch (error) {
  // 🔄 Always restore directories even if build fails
  restoreApiDirectory();
  restoreMobileIncompatiblePages();
  
  console.error(`\n❌ Build failed for ${target}:`);
  console.error(`💥 Error: ${error.message}`);
  
  // 🔧 Helpful error suggestions
  if (error.message.includes('export')) {
    console.error('\n💡 Static export troubleshooting:');
    console.error('  1. Check for pages using server-side features (cookies, headers)');
    console.error('  2. Ensure all pages are either "use client" or have proper static exports');
    console.error('  3. Review next.config.ts for proper export configuration');
  }
  
  if (error.message.includes('electron')) {
    console.error('\n💡 Electron build troubleshooting:');
    console.error('  1. Ensure electron directory exists and is properly set up');
    console.error('  2. Check electron/package.json for correct configuration');
    console.error('  3. Verify copy-out-to-electron.js script is working');
  }
  
  process.exit(1);
} 