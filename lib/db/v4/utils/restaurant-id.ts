"use client";

/**
 * V4 Restaurant ID Utilities
 *
 * Provides utilities for managing restaurant IDs in the v4 database.
 */

/**
 * Get the current restaurant ID from the authenticated user's session
 * This is the ONLY source of truth for the restaurant ID
 */
export function getCurrentRestaurantId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Get restaurant ID from auth_data - this is the ONLY source of truth
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        if (parsedData.restaurantId) {
          console.log(`V4 Utils - Found restaurant ID in auth session: ${parsedData.restaurantId}`);
          return parsedData.restaurantId;
        }
      } catch (parseError) {
        console.error('Error parsing auth_data:', parseError);
      }
    }

    console.warn('V4 Utils - No restaurant ID found in auth session');
    return null;
  } catch (error) {
    console.error('Error getting current restaurant ID from auth session:', error);
    return null;
  }
}

/**
 * Force a specific restaurant ID to be used
 * This should only be used in special circumstances when the ID needs to be set programmatically
 * 
 * @param restaurantId The restaurant ID to force
 */
export function forceRestaurantId(restaurantId: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Get existing auth data
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        // Update the restaurant ID
        parsedData.restaurantId = restaurantId;
        // Save back to localStorage
        localStorage.setItem('auth_data', JSON.stringify(parsedData));
        console.log(`V4 Utils - Forced restaurant ID: ${restaurantId}`);
      } catch (parseError) {
        console.error('Error parsing auth_data for force update:', parseError);
      }
    } else {
      // Create minimal auth data with just the restaurant ID
      const minimalAuthData = { restaurantId };
      localStorage.setItem('auth_data', JSON.stringify(minimalAuthData));
      console.log(`V4 Utils - Created new auth data with forced restaurant ID: ${restaurantId}`);
    }

    // Also update cookie for cross-tab consistency
    document.cookie = `auth_restaurant_id=${restaurantId}; path=/; max-age=********; SameSite=Lax`;
    console.log(`V4 Utils - Updated cookie with restaurant ID: ${restaurantId}`);
  } catch (error) {
    console.error('Error forcing restaurant ID:', error);
  }
}

/**
 * Clear all cached restaurant ID data (for account switching)
 * This ensures stale restaurant IDs don't persist when switching accounts
 */
export function clearCachedRestaurantId(): void {
  if (typeof window === 'undefined') return;

  try {
    console.log('🧹 [clearCachedRestaurantId] Clearing all cached restaurant ID data...');

    // Clear cookie
    document.cookie = 'auth_restaurant_id=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

    // Clear from localStorage auth_data
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        delete parsedData.restaurantId;
        localStorage.setItem('auth_data', JSON.stringify(parsedData));
        console.log('🧹 [clearCachedRestaurantId] Cleared restaurant ID from localStorage auth_data');
      } catch (parseError) {
        console.error('Error parsing auth_data for clearing:', parseError);
      }
    }

    // Clear any other potential restaurant ID storage
    localStorage.removeItem('restaurantId');

    console.log('🧹 [clearCachedRestaurantId] All cached restaurant ID data cleared');
  } catch (error) {
    console.error('❌ [clearCachedRestaurantId] Error clearing cached restaurant ID:', error);
  }
}

/**
 * Update restaurant ID when switching accounts
 * This properly handles the transition from one restaurant to another
 */
export function updateRestaurantIdForAccountSwitch(newRestaurantId: string): void {
  if (typeof window === 'undefined') return;

  console.log(`🔄 [updateRestaurantIdForAccountSwitch] Switching to restaurant ID: ${newRestaurantId}`);

  // First clear all cached data
  clearCachedRestaurantId();

  // Then set the new restaurant ID
  forceRestaurantId(newRestaurantId);

  // Force database reinitialization with the new restaurant ID
  // This ensures the database system switches to the correct database
  if (typeof window !== 'undefined') {
    // Dispatch a custom event to notify the database system of the restaurant change
    const event = new CustomEvent('restaurant-id-changed', {
      detail: {
        newRestaurantId,
        timestamp: Date.now(),
        source: 'account-switch'
      }
    });
    document.dispatchEvent(event);

    // Also try to reinitialize the main database instance directly
    import('@/lib/db/v4/core/db-main-instance').then(({ mainDbInstance }) => {
      console.log(`🔄 [updateRestaurantIdForAccountSwitch] Forcing database reinitialization for: ${newRestaurantId}`);

      // Force cleanup and reinitialization
      mainDbInstance.cleanup().then(() => {
        return mainDbInstance.initialize(newRestaurantId);
      }).then(() => {
        console.log(`✅ [updateRestaurantIdForAccountSwitch] Database reinitialized for: ${newRestaurantId}`);
      }).catch(error => {
        console.error(`❌ [updateRestaurantIdForAccountSwitch] Database reinitialization failed:`, error);
      });
    }).catch(error => {
      console.error('❌ [updateRestaurantIdForAccountSwitch] Failed to import database instance:', error);
    });
  }

  console.log(`✅ [updateRestaurantIdForAccountSwitch] Account switch complete for restaurant: ${newRestaurantId}`);
}


