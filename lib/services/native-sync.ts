import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';
import { getRestaurantDbName } from '@/lib/db/db-utils';

interface SyncServer {
  ip: string;
  port: number;
  url: string;
}

interface SyncStatus {
  connected: boolean;
  syncing: boolean;
  lastSync?: Date;
  error?: string;
  docsReceived: number;
  docsSent: number;
}

interface SyncOptions {
  live?: boolean;
  retry?: boolean;
  auth?: {
    username: string;
    password: string;
  };
}

class NativeSyncService {
  private syncHandler: any = null;
  private currentServer: SyncServer | null = null;
  private status: SyncStatus = {
    connected: false,
    syncing: false,
    docsReceived: 0,
    docsSent: 0
  };
  private listeners: ((status: SyncStatus) => void)[] = [];

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  onStatusChange(listener: (status: SyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  async startSync(server: SyncServer, options: SyncOptions = {}): Promise<boolean> {
    console.log(`🔄 Starting sync with ${server.url}...`);

    // Prevent syncing to self (localhost, 127.0.0.1, or local network IP)
    if (this.isSelfSync(server)) {
      console.warn(`⚠️ Skipping sync to self: ${server.url}`);
      this.status.error = 'Cannot sync to self - same machine detected';
      this.emitStatusUpdate();
      return false;
    }

    try {
      console.log('🔍 Checking database status before sync...');
      console.log('Database initialized:', mainDbInstance.isInitialized);
      console.log('Restaurant ID:', mainDbInstance.getCurrentRestaurantId());
      
      await this.waitForDatabase();
      
      const localDb = mainDbInstance.getPouchDBForSync();
      if (!localDb) {
        const dbStatus = {
          isInitialized: mainDbInstance.isInitialized,
          restaurantId: mainDbInstance.getCurrentRestaurantId(),
          hasDatabase: Boolean(mainDbInstance.getDatabase())
        };
        console.error('❌ Database status:', dbStatus);
        throw new Error(`Local database not available for sync. Status: ${JSON.stringify(dbStatus)}`);
      }

      const restaurantId = mainDbInstance.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Restaurant ID not available');
      }

      const dbName = getRestaurantDbName(restaurantId);

      // 🔐 CRITICAL FIX: Include CouchDB admin credentials for authentication
      // The desktop CouchDB server is configured with admin:admin credentials
      const remoteUrl = `http://admin:admin@${server.ip}:${server.port}/${dbName}`;

      console.log(`📡 Syncing ${dbName} with authenticated URL: http://admin:***@${server.ip}:${server.port}/${dbName}`);
      console.log(`🔐 [AUTH FIX] Using admin:admin credentials for CouchDB authentication`);

      const syncOptions = {
        live: options.live ?? true,
        retry: options.retry ?? true,
        ...options
      };

      this.currentServer = server;
      this.status = {
        connected: true,
        syncing: true,
        docsReceived: 0,
        docsSent: 0
      };
      this.emitStatusUpdate();

      this.syncHandler = localDb.sync(remoteUrl, syncOptions)
        .on('change', (info: any) => {
          console.log('📊 Sync change:', info);
          if (info.direction === 'pull') {
            this.status.docsReceived += info.change?.docs_read || 0;
          } else if (info.direction === 'push') {
            this.status.docsSent += info.change?.docs_written || 0;
          }
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('paused', () => {
          console.log('⏸️ Sync paused');
          this.status.syncing = false;
          this.emitStatusUpdate();
        })
        .on('active', () => {
          console.log('▶️ Sync active');
          this.status.syncing = true;
          this.emitStatusUpdate();
        })
        .on('denied', (err: any) => {
          console.error('❌ Sync denied:', err);
          console.error('❌ Denial details:', {
            message: err.message,
            status: err.status,
            name: err.name,
            error: err.error,
            reason: err.reason
          });
          this.status.error = `Access denied: ${err.message || err.reason || 'Authentication failed'}`;
          this.emitStatusUpdate();
        })
        .on('complete', (info: any) => {
          console.log('✅ Sync complete:', info);
          this.status.syncing = false;
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('error', (err: any) => {
          console.error('❌ Sync error:', err);
          console.error('❌ Full error details:', {
            message: err.message,
            status: err.status,
            name: err.name,
            error: err.error,
            reason: err.reason,
            docId: err.docId,
            rev: err.rev
          });

          // 🔐 Enhanced error messages for common authentication issues
          let errorMessage = err.message || err.reason || 'Unknown error';
          if (err.status === 401 || err.name === 'unauthorized' ||
              (err.message && err.message.toLowerCase().includes('unauthorized'))) {
            errorMessage = `Authentication failed: You are not authorized to access this database. Check CouchDB credentials (admin:admin).`;
          } else if (err.status === 403 || err.name === 'forbidden') {
            errorMessage = `Access forbidden: Database access denied. Check permissions and credentials.`;
          } else if (err.status === 404) {
            errorMessage = `Database not found: The database '${dbName}' does not exist on the server.`;
          } else if (err.name === 'timeout' || err.message?.includes('timeout')) {
            errorMessage = `Connection timeout: Unable to reach CouchDB server at ${this.currentServer?.ip}:${this.currentServer?.port}`;
          }

          this.status.error = `Sync failed: ${errorMessage}`;
          this.status.connected = false;
          this.status.syncing = false;
          this.emitStatusUpdate();
        });

      return true;
    } catch (error: any) {
      console.error('❌ Failed to start sync:', error);
      this.status.error = error.message;
      this.status.connected = false;
      this.status.syncing = false;
      this.emitStatusUpdate();
      return false;
    }
  }

  async stopSync(): Promise<void> {
    if (this.syncHandler) {
      console.log('🛑 Stopping sync...');
      this.syncHandler.cancel();
      this.syncHandler = null;
    }

    this.currentServer = null;
    this.status = {
      connected: false,
      syncing: false,
      docsReceived: this.status.docsReceived,
      docsSent: this.status.docsSent,
      lastSync: this.status.lastSync
    };
    this.emitStatusUpdate();
  }

  private isSelfSync(server: SyncServer): boolean {
    // ALLOW localhost connections in Electron - the CouchDB server runs locally
    // Check multiple ways to detect Electron environment
    if (server.ip === 'localhost' || server.ip === '127.0.0.1') {
      const isElectron = typeof window !== 'undefined' && (
        (window as any).electron?.isElectron ||
        (window as any).IS_DESKTOP_APP ||
        navigator.userAgent.includes('Electron')
      );
      
      if (isElectron) {
        console.log(`✅ Allowing Electron localhost sync: ${server.ip}:${server.port}`);
        return false;
      }
      console.log(`🔍 Blocking web browser self-sync to localhost: ${server.ip}:${server.port}`);
      return true;
    }

    // Get current machine's local IP (simplified check)
    if (typeof window !== 'undefined') {
      // In browser, we can't easily get local IP, but we can check common patterns
      const currentHost = window.location.hostname;
      if (currentHost === server.ip) {
        console.log(`🔍 Blocking self-sync to current host: ${server.ip}:${server.port}`);
        return true;
      }
    }

    // For Electron apps, only block if we can definitively confirm it's the same machine
    // Don't block all local network IPs as they could be legitimate remote servers
    console.log(`✅ Allowing sync to ${server.ip}:${server.port} (not detected as self)`);
    return false;
  }

  private async waitForDatabase(maxWaitMs = 10000): Promise<void> {
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized || !mainDbInstance.getPouchDBForSync()) {
      if (Date.now() - startTime > maxWaitMs) {
        console.error('❌ Database initialization timeout. Status:', {
          isInitialized: mainDbInstance.isInitialized,
          hasRestaurantId: Boolean(mainDbInstance.getCurrentRestaurantId()),
          hasDatabase: Boolean(mainDbInstance.getDatabase()),
          hasPouchDB: Boolean(mainDbInstance.getPouchDBForSync())
        });
        throw new Error('Database initialization timeout - not ready for sync operations');
      }
      
      console.log('⏳ Waiting for database initialization...', {
        isInitialized: mainDbInstance.isInitialized,
        hasRestaurantId: Boolean(mainDbInstance.getCurrentRestaurantId()),
        waitTime: Date.now() - startTime
      });
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('✅ Database ready for sync operations');
  }

  getStatus(): SyncStatus {
    return { ...this.status };
  }

  getCurrentServer(): SyncServer | null {
    return this.currentServer;
  }

  isConnected(): boolean {
    return this.status.connected;
  }

  isSyncing(): boolean {
    return this.status.syncing;
  }
}

export const nativeSyncService = new NativeSyncService();
export type { SyncServer, SyncStatus, SyncOptions };