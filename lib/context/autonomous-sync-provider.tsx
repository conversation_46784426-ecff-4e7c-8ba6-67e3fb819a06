'use client';

import React, { createContext, useContext, ReactNode } from 'react';

// Stub types for removed autonomous sync service
interface AutoSyncStatus {
  enabled: boolean;
  isRunning: boolean;
  lastDiscovery: Date | null;
  discoveredPeers: any[];
  activeSyncs: any[];
  errors: any[];
}

interface AutonomousSyncContextType {
  status: AutoSyncStatus;
  manualDiscovery: () => Promise<void>;
  enable: () => void;
  disable: () => void;
}

const AutonomousSyncContext = createContext<AutonomousSyncContextType | undefined>(undefined);

interface AutonomousSyncProviderProps {
  children: ReactNode;
}

export function AutonomousSyncProvider({ children }: AutonomousSyncProviderProps) {
  // Stub implementation - autonomous sync service was removed
  const stubStatus: AutoSyncStatus = {
    enabled: false,
    isRunning: false,
    lastDiscovery: null,
    discoveredPeers: [],
    activeSyncs: [],
    errors: []
  };

  const contextValue: AutonomousSyncContextType = {
    status: stubStatus,
    manualDiscovery: async () => {
      console.log('Autonomous sync service was removed - manual discovery is not available');
    },
    enable: () => {
      console.log('Autonomous sync service was removed - enable is not available');
    },
    disable: () => {
      console.log('Autonomous sync service was removed - disable is not available');
    }
  };

  return (
    <AutonomousSyncContext.Provider value={contextValue}>
      {children}
    </AutonomousSyncContext.Provider>
  );
}

export function useAutonomousSync(): AutonomousSyncContextType {
  const context = useContext(AutonomousSyncContext);
  if (context === undefined) {
    throw new Error('useAutonomousSync must be used within an AutonomousSyncProvider');
  }
  return context;
}