"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { getCurrentRestaurantId, clearCachedRestaurantId, updateRestaurantIdForAccountSwitch } from '@/lib/db/v4/utils/restaurant-id';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

/**
 * Debug component to diagnose restaurant ID caching issues
 * This helps identify when mobile devices are using stale restaurant IDs
 */
export function RestaurantIdDebug() {
  const { user, isAuthenticated } = useAuth();
  const { currentDbRestaurantId, isDbInitialized } = useUnifiedDB();
  
  const [debugInfo, setDebugInfo] = useState({
    authRestaurantId: '',
    storageRestaurantId: '',
    dbRestaurantId: '',
    cookieRestaurantId: '',
    authData: '',
    dbInitialized: false,
    timestamp: Date.now()
  });

  // Refresh debug info
  const refreshDebugInfo = () => {
    const authRestaurantId = user?.restaurantId || 'None';
    const storageRestaurantId = getCurrentRestaurantId() || 'None';
    const dbRestaurantId = currentDbRestaurantId || 'None';
    
    // Get cookie restaurant ID
    let cookieRestaurantId = 'None';
    if (typeof document !== 'undefined') {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'auth_restaurant_id') {
          cookieRestaurantId = decodeURIComponent(value);
          break;
        }
      }
    }
    
    // Get auth data from localStorage
    let authData = 'None';
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('auth_data');
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          authData = JSON.stringify(parsed, null, 2);
        } catch (e) {
          authData = 'Invalid JSON';
        }
      }
    }

    setDebugInfo({
      authRestaurantId,
      storageRestaurantId,
      dbRestaurantId,
      cookieRestaurantId,
      authData,
      dbInitialized: isDbInitialized,
      timestamp: Date.now()
    });
  };

  // Auto-refresh every 2 seconds
  useEffect(() => {
    refreshDebugInfo();
    const interval = setInterval(refreshDebugInfo, 2000);
    return () => clearInterval(interval);
  }, [user, currentDbRestaurantId, isDbInitialized]);

  // Test functions
  const handleClearCache = () => {
    clearCachedRestaurantId();
    setTimeout(refreshDebugInfo, 100);
  };

  const handleForceSwitch = () => {
    if (user?.restaurantId) {
      updateRestaurantIdForAccountSwitch(user.restaurantId);
      setTimeout(refreshDebugInfo, 100);
    }
  };

  const handleForceDbReinit = async () => {
    if (user?.restaurantId) {
      try {
        await mainDbInstance.cleanup();
        await mainDbInstance.initialize(user.restaurantId);
        setTimeout(refreshDebugInfo, 100);
      } catch (error) {
        console.error('Failed to reinitialize database:', error);
      }
    }
  };

  // Check for inconsistencies
  const hasInconsistency = debugInfo.authRestaurantId !== debugInfo.dbRestaurantId && 
                          debugInfo.authRestaurantId !== 'None' && 
                          debugInfo.dbRestaurantId !== 'None';

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔍 Restaurant ID Debug
          {hasInconsistency && (
            <Badge variant="destructive">Inconsistency Detected!</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-sm text-muted-foreground">Auth User</div>
            <div className="font-mono text-xs bg-muted p-2 rounded">
              {debugInfo.authRestaurantId}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-muted-foreground">Storage</div>
            <div className="font-mono text-xs bg-muted p-2 rounded">
              {debugInfo.storageRestaurantId}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-muted-foreground">Database</div>
            <div className="font-mono text-xs bg-muted p-2 rounded">
              {debugInfo.dbRestaurantId}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-muted-foreground">Cookie</div>
            <div className="font-mono text-xs bg-muted p-2 rounded">
              {debugInfo.cookieRestaurantId}
            </div>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="flex gap-2 flex-wrap">
          <Badge variant={isAuthenticated ? "default" : "secondary"}>
            Auth: {isAuthenticated ? "✅" : "❌"}
          </Badge>
          <Badge variant={debugInfo.dbInitialized ? "default" : "secondary"}>
            DB: {debugInfo.dbInitialized ? "✅" : "❌"}
          </Badge>
          <Badge variant={hasInconsistency ? "destructive" : "default"}>
            Sync: {hasInconsistency ? "❌" : "✅"}
          </Badge>
        </div>

        {/* Inconsistency Warning */}
        {hasInconsistency && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <h4 className="font-semibold text-destructive mb-2">⚠️ Restaurant ID Mismatch Detected</h4>
            <p className="text-sm text-muted-foreground mb-3">
              The authenticated user's restaurant ID doesn't match the database restaurant ID. 
              This is the exact issue you identified - the mobile app is using a stale restaurant ID.
            </p>
            <Button onClick={handleForceSwitch} variant="destructive" size="sm">
              🔧 Fix Restaurant ID Mismatch
            </Button>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 flex-wrap">
          <Button onClick={refreshDebugInfo} variant="outline" size="sm">
            🔄 Refresh
          </Button>
          <Button onClick={handleClearCache} variant="outline" size="sm">
            🧹 Clear Cache
          </Button>
          <Button onClick={handleForceSwitch} variant="outline" size="sm">
            🔄 Force Switch
          </Button>
          <Button onClick={handleForceDbReinit} variant="outline" size="sm">
            🗃️ Reinit DB
          </Button>
        </div>

        {/* Raw Data */}
        <details className="text-xs">
          <summary className="cursor-pointer text-muted-foreground">Raw Auth Data</summary>
          <pre className="mt-2 bg-muted p-2 rounded overflow-auto max-h-40">
            {debugInfo.authData}
          </pre>
        </details>

        <div className="text-xs text-muted-foreground">
          Last updated: {new Date(debugInfo.timestamp).toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
}
