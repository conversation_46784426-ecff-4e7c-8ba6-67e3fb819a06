# Mobile Restaurant ID Cache Fix

## Problem Identified ✅

**Issue**: Mobile app was using stale restaurant ID from previous account sessions, causing sync to wrong database.

**Symptoms**:
- User switches from account with restaurant ID `x` to account with restaurant ID `y`
- Desktop correctly switches to database `resto-y`
- Mobile continues using cached restaurant ID `x` and syncs with database `resto-x`
- Result: Apps show different data despite successful sync transport

## Root Cause Analysis

### Caching Hierarchy Issue
The mobile app used this restaurant ID retrieval hierarchy:
1. **Cookies** (persistent across sessions)
2. **localStorage auth_data** (not cleared on account switch)
3. **Current auth session** (correct but overridden by cache)

### Missing Cleanup
When switching accounts, the system didn't:
- Clear cached restaurant ID from cookies
- Clear restaurant ID from localStorage
- Force database reinitialization with new restaurant ID

## Solution Implemented ✅

### 1. Restaurant ID Cleanup Functions
**File**: `lib/db/v4/utils/restaurant-id.ts`

Added three new functions:
- `clearCachedRestaurantId()` - Clears all cached restaurant ID data
- `updateRestaurantIdForAccountSwitch()` - Handles complete account switching
- Enhanced `forceRestaurantId()` - Now also updates cookies

### 2. Account Switch Integration
**File**: `lib/auth/multi-user-session-manager.ts`

Modified `switchToUser()` and `addSession()` to:
- Detect restaurant ID changes
- Automatically call cleanup functions
- Force database reinitialization
- Update storage with new restaurant ID

### 3. Database Reinitialization
**File**: `lib/context/unified-db-provider.tsx`

Added event listener for `restaurant-id-changed` events:
- Resets all database state
- Forces complete reinitialization
- Ensures correct database connection

### 4. Debug Component
**File**: `components/debug/RestaurantIdDebug.tsx`

Created diagnostic tool to:
- Show all restaurant ID sources
- Detect inconsistencies
- Provide manual fix buttons
- Monitor real-time status

## How The Fix Works

### Account Switch Flow
1. **User switches accounts** (different restaurant IDs)
2. **Session manager detects** restaurant ID change
3. **Cleanup function called** automatically:
   - Clears cookies: `auth_restaurant_id`
   - Clears localStorage: `auth_data.restaurantId`
   - Removes other cached data
4. **New restaurant ID set** in all storage locations
5. **Database reinitialization triggered**:
   - Cleanup old database connection
   - Initialize with new restaurant ID
   - Reset all database state
6. **Event dispatched** to notify UI components
7. **UnifiedDBProvider resets** and reinitializes

### Automatic Detection
The fix automatically detects:
- Account switches with different restaurant IDs
- Stale cached data
- Database inconsistencies

## Testing The Fix

### Debug Interface
Navigate to: **Debug Tools** page (`/debug`)

The Restaurant ID Debug component shows:
- **Auth User**: Restaurant ID from current user session
- **Storage**: Restaurant ID from localStorage
- **Database**: Restaurant ID from database instance
- **Cookie**: Restaurant ID from browser cookies

### Inconsistency Detection
- **Green checkmark**: All IDs match (working correctly)
- **Red warning**: Mismatch detected (the original problem)
- **Fix button**: Manually trigger the cleanup process

### Manual Testing
1. Login with account A (restaurant ID x)
2. Check debug panel - all should show `x`
3. Switch to account B (restaurant ID y)
4. Check debug panel - all should show `y`
5. If mismatch detected, click "Fix Restaurant ID Mismatch"

## Files Modified

### Core Fix Files
- `lib/db/v4/utils/restaurant-id.ts` - Cleanup functions
- `lib/auth/multi-user-session-manager.ts` - Account switch detection
- `lib/context/unified-db-provider.tsx` - Database reinitialization

### Debug/Testing Files
- `components/debug/RestaurantIdDebug.tsx` - Diagnostic component
- `app/(protected)/debug/page.tsx` - Added debug interface

## Prevention Measures

### Automatic Cleanup
- Account switches now automatically trigger cleanup
- No manual intervention required
- Works for both login and user switching

### Event-Driven Architecture
- Uses custom events for loose coupling
- Database system responds to restaurant ID changes
- UI components can listen for updates

### Comprehensive Cache Clearing
- Clears all known storage locations
- Handles cookies, localStorage, and memory cache
- Prevents any stale data persistence

## Verification

To verify the fix is working:

1. **Check Debug Panel**: All restaurant IDs should match
2. **Test Account Switch**: IDs should update immediately
3. **Verify Sync**: Mobile and desktop should show same data
4. **Check Database Names**: CouchDB should show correct `resto-{id}` databases

The fix ensures mobile devices always use the correct restaurant ID and sync with the right database, eliminating the data inconsistency issue.
